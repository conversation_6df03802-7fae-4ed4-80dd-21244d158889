<?php

namespace App\Admin\Metrics\DataStatistics;

use App\Models\Channel;
use App\Models\DirectoryCc;
use App\Models\DirectoryCountry;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\MerchantUrl;
use App\Models\RiskMcc;
use Dcat\Admin\Widgets\Box;
use App\Models\MerchantBusiness;
use Illuminate\Support\Arr;

class RiskAnalysis
{


    public function handle()
    {
        $midList = Merchant::pluck('merchant_name', 'merchant_id')
            ->map(static function ($item, $key) {
                return $item . ':' . $key;
            })->toArray();
        $midList = Arr::prepend($midList, '全部', '全部');
        $midList = Arr::prepend($midList, '搜索', '搜索');

        $mid = [
            "id"      => "rickMid",
            "options" => $midList
        ];

        $midMenu = TranDropdown::make($mid)->button('merchant_id')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="risk-drop-search-mid" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='switch-bar-mid' data-mid='{$k}'>{$v}</a>";
            });

        $Month = [];

        for ($i = 1; $i <= 12; $i++) {
            $Month[$i] = "近" . $i . "个月";
        }

        $mouth = [
            "id"      => "mouth",
            "options" => $Month
        ];

        $MonthMenu = TranDropdown::make($mouth)->button("近6个月")->click()
            ->map(function ($v, $k) {
                return "<a class='switch-bar' data-option='{$k}'>{$v}</a>";
            });

        $businessList = MerchantBusiness::get();
        $bidList      = $businessList->pluck('business_id')->toArray();
        $bidList      = Arr::prepend($bidList, '全部');
        $bidList      = Arr::prepend($bidList, '搜索');

        $bid = [
            "id"      => "rickBid",
            "options" => $bidList
        ];

        $bidMenu = TranDropdown::make($bid)->button('business_id')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="risk-drop-search-bid" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }

                return "<a class='switch-bar-bid' data-bid='{$k}'>{$v}</a>";
            });

        $urlList    = MerchantUrl::get();
        $merUrlList = $urlList->pluck('url_name', 'id')->toArray();
        $merUrlList = [0 => '搜索', -1 => '全部'] + $merUrlList;

        $mer = [
            "id"      => "rickUrl",
            "options" => $merUrlList
        ];

        $merUrlMenu = TranDropdown::make($mer)->button('url_name')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="risk-drop-search-url" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }

                return "<a class='switch-bar-url' data-url='{$k}'>{$v}</a>";
            });

        $dirList = RiskMcc::query()->where('overall_risk_rating', '!=', 'Prohibited')->pluck('mcc', 'id')->toArray();
        $dirList = ['全部'] + $dirList;

        $dir = [
            "id"      => "rickMcc",
            "options" => $dirList
        ];

        $dirMenu = TranDropdown::make($dir)->button('MCC')->click()
            ->map(function ($v, $k) {
                return "<a class='switch-bar-mcc' data-mcc='{$k}'>{$v}</a>";
            });

        $dirCurrList = DirectoryCurrency::pluck('code')->toArray();
        $dirCurrList = Arr::prepend($dirCurrList, '全部');
        $dirCurrList = Arr::prepend($dirCurrList, '搜索');

        $dirCurr = [
            "id"      => "rickCurr",
            "options" => $dirCurrList
        ];

        $dirCurrMenu = TranDropdown::make($dirCurr)->button('currency')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="risk-drop-search-curr" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='switch-bar-curr' data-curr='{$k}'>{$v}</a>";
            });

        $dirCcsList = DirectoryCc::isRiskControl()->pluck('cc_type')->toArray();
        $dirCcsList = Arr::prepend($dirCcsList, '全部');

        $dirCcs = [
            "id"      => "rickCcs",
            "options" => $dirCcsList
        ];

        $dirCcsMenu = TranDropdown::make($dirCcs)->button('cc_type')->click()
            ->map(function ($v, $k) {
                return "<a class='switch-bar-ccs' data-ccs='{$k}'>{$v}</a>";
            });

        $CountryList = DirectoryCountry::pluck('name')->toArray();
        $CountryList = Arr::prepend($CountryList, '全部');
        $CountryList = Arr::prepend($CountryList, '搜索');
        $Country     = [
            "id"      => "rickCountry",
            "options" => $CountryList
        ];

        $countryMenu = TranDropdown::make($Country)->button('card_country')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="risk-drop-search-country" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='switch-bar-country' data-country='{$k}'>{$v}</a>";
            });

        $ChannelList = Channel::pluck('id', 'channel')->toArray();
        $ChannelList = Arr::prepend($ChannelList, '全部');
        $ChannelList = Arr::prepend($ChannelList, '搜索');

        $Channel = [
            "id"      => "rickChannel",
            "options" => $ChannelList
        ];

        $ChannelMenu = TranDropdown::make($Channel)->button('channel')->click()
            ->map(function ($v, $k) {
                if ($v == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="risk-drop-search-channel" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                if ($v == '全部') {
                    return "<a class='switch-bar-channel' data-trancountry='{$v}'>全部</a>";
                }
                return "<a class='switch-bar-channel' data-trancountry='{$v}'>{$k}</a>";
            });

        $SupplierList = Channel::query()->with('channelSupplier')->get();
        $SupplierList = $SupplierList->pluck('channelSupplier.supplier_name', 'id')->toArray();
        $SupplierList = Arr::prepend($SupplierList, '全部', '全部');
        $SupplierList = Arr::prepend($SupplierList, '搜索', '搜索');

        $Channel = [
            "id"      => "rickSupplier",
            "options" => array_unique($SupplierList)
        ];

        $SupplierMenu = TranDropdown::make($Channel)->button('supplier')->click()
            ->map(function ($v, $k) {
                if ($v == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="risk-drop-search-supplier" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                if ($v == '全部') {
                    return "<a class='switch-bar-supplier' data-trancountry='{$v}'>全部</a>";
                }
                return "<a class='switch-bar-supplier' data-trancountry='{$k}'>{$v}</a>";
            });

        $chart = RiskChart::make([
            "supplier" => $SupplierList
        ])->title("风险分析")
            ->fetching('$("#risk-box").loading()')
            ->fetched('$("#risk-box").loading(false)')
            ->fetched(self::JS())
            ->click(['.switch-bar-mid', '.switch-bar', '.switch-bar-bid', '.switch-bar-url', '.switch-bar-mcc', '.switch-bar-curr', '.switch-bar-ccs', '.switch-bar-country', '.switch-bar-channel', '.switch-bar-supplier']);

        $box = Box::make()->title("风险分析")->id("risk-box");

        $box->content(
            $MonthMenu->render() . '&nbsp; ' . $midMenu->render() . '&nbsp; ' . $bidMenu->render() . '&nbsp; ' . $merUrlMenu->render() . '&nbsp;'
                . $dirMenu->render() . '&nbsp;' . $dirCurrMenu->render() . '&nbsp;' . $dirCcsMenu->render() . '&nbsp;' . $countryMenu->render() . '&nbsp;'
                . $SupplierMenu->render() . '&nbsp;' . $ChannelMenu->render() . '&nbsp;' . $chart->render()
        );
        return $box;
    }

    private static function JS()
    {
        //获取BID
        $business      = MerchantBusiness::selectRaw('business_id,merchant_id')->get()->toArray();
        $business_data = [];

        foreach ($business as $vo) {
            $business_data[$vo['merchant_id']][] = $vo['business_id'];
        }

        $business_data = json_encode($business_data);

        //获取账单标识
        $channel      = Channel::with('channelSupplier')->get()->toArray();
        $channel_data = [];
        foreach ($channel as $vo) {
            $channel_data[$vo['channel_supplier']['supplier_name']][] = $vo['channel'];
        }

        $channel_data = json_encode($channel_data);

        $JS = <<<JS
        //BID联动
        $('#rickBid').on('click', function(){
            let value        = $('#rickMid stub').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            let businessData = {$business_data};

            if (value != 'merchant_id' && value != '全部') {
                value        = value.split(":");
                let business = businessData[value[1]];
                let html     = '';
                let ul       = $('#rickBid').next('');

                for(let i = 0; i < business.length; i++) {
                    html += '<li class="dropdown-item" style="display: block;"><a class="switch-bar-bid" data-tranbid="'+ (i + 2) +'">'+ business[i] +'</a></li>'
                }

                ul.children().remove('li:gt(1)');
                ul.append(html);
            }
        })

        //账单标识联动
        $('#rickChannel').on('click', function(){
            let value        = $('#rickSupplier stub').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            let channel_data = {$channel_data};

            if (value != 'supplier' && value != '全部') {
                let channel = channel_data[value];
                let html     = '';
                let ul       = $('#rickChannel').next('');

                for(let i = 0; i < channel.length; i++) {
                    html += '<li class="dropdown-item" style="display: block;"><a class="switch-bar-channel" data-tranbid="'+ (i + 2) +'">'+ channel[i] +'</a></li>'
                }

                ul.children().remove('li:gt(1)');
                ul.append(html);
            }
        })

        $(".dropdown-menu").css({"height" : "350px", "overflow-y" : "auto"})
        let aClass  = ['switch-bar-curr', 'switch-bar-country', 'switch-bar-mid','switch-bar-bid','switch-bar-url','switch-bar-channel','switch-bar-supplier'];
        let aId     = ['rickCurr', 'rickCountry','rickMid','rickBid','rickUrl','rickChannel','rickSupplier']
        let aSearch = ['risk-drop-search-curr', 'risk-drop-search-country', 'risk-drop-search-mid', 'risk-drop-search-bid', 'risk-drop-search-url','risk-drop-search-channel','risk-drop-search-supplier']
        for(let i = 0; i < aSearch.length; i++) {
            let child = $("#"+aId[i]).next().find('.'+aClass[i])
             child.parent().css("display","block");
            $("#"+aSearch[i]).click(function () {
                event.stopPropagation();
            }).bind("input propertychange", function() {
                let child = $("#"+aId[i]).next().find('.'+aClass[i])
                for(let j = 0; j < child.length; j++) {
                    if (child[j].text.toLowerCase().indexOf($(this).val().toLowerCase()) > -1) {
                        child[j].parentNode.style.display = "block";
                    } else {
                        child[j].parentNode.style.display = "none";
                    }
                }
             }).val("").parent().click(function() {
                 event.stopPropagation();
             }).parent().css("padding",'0').removeAttr("href");
        }
JS;
        return $JS;
    }
}
