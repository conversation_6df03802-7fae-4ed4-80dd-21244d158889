<?php

namespace App\Merchant\Controllers;

use App\Jobs\SendEmail;
use App\Jobs\SendSlsLog;
use App\Merchant\Repositories\MerchantKyc;
use App\Models\DirectoryCountry;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\MerchantKycBeneficial;
use App\Models\OpenImUser;
use App\Models\RiskCase;
use App\Models\User;
use App\Services\KycService;
use App\Services\LexisNexisService;
use App\Services\OpenImService;
use App\Services\RiskCasesService;
use Carbon\Carbon;
use Dcat\Admin\Admin;
use Dcat\Admin\Form\Row;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\MerchantKyc as MerchantKycModel;
use Dcat\Admin\Form;
use Dcat\Admin\Layout\Content;
use Exception;
use Illuminate\Support\Facades\DB;

class KycController extends AdminController
{
    protected $description = [
        'create' => '注册',
        'index'  => '信息'
    ];

    protected $with = [];

    public function __construct()
    {
        $this->with = ['ubo' => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_UBO);
        }, 'director'        => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_DIRECTOR);
        }, 'authorized'      => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_AUTHORITY);
        }];
    }

    public function index(Content $content)
    {
        $user = auth()->user();
        if (!$user || $user->merchant->status == Merchant::STATUS_ENABLE) {
            return redirect('/merchant');
        }

        $kyc = MerchantKycModel::with($this->with)->where('merchant_id', $user->merchant_id)
                               ->orderBy('id', 'desc')
                               ->first();

        return $content
            ->translation($this->translation())
            ->title($this->title())
            ->description($this->description()['index'] ?? trans('admin.list'))
            ->body($this->form($kyc));
    }

    protected function form($kyc = null)
    {
        return Form::make(new MerchantKyc($this->with), function (Form $form) use ($kyc) {
            $form->disableHeader();
            $form->footer(function ($footer) {
                // 去掉`重置`按钮
                $footer->disableReset();
                // 去掉`查看`checkbox
                $footer->disableViewCheck();
                // 去掉`继续编辑`checkbox
                $footer->disableEditingCheck();
                // 去掉`继续创建`checkbox
                $footer->disableCreatingCheck();
            });
            Admin::js('/js/form-scroll.js');
            Admin::script(
                <<<JS
                    // 初次打开，如果已经有错误
                    scrollToFirstErrorField();

                    $(document).ajaxComplete(function (event, jqxhr, settings) {
                        if (jqxhr.status === 422){
                            scrollToFirstErrorField();
                        }
                    });

                    // UBO自动复制到Director功能
                    function copyUboToDirector(uboContainer) {
                        // 获取UBO数据
                        var uboData = {};

                        // 收集UBO表单数据
                        var fields = ['cert_type', 'nationality', 'name', 'share_ratio', 'birth_date', 'address', 'cert_number', 'is_signatory'];
                        fields.forEach(function(field) {
                            var element = uboContainer.find('[name*="[' + field + ']"]');
                            if (element.length > 0) {
                                if (element.is('select') || element.is('input[type="text"]') || element.is('input[type="date"]') || element.is('textarea')) {
                                    uboData[field] = element.val();
                                } else if (element.is('input[type="radio"]:checked')) {
                                    uboData[field] = element.val();
                                }
                            }
                        });

                        // 处理文件字段（这些字段可能需要特殊处理）
                        var certFileElement = uboContainer.find('[name*="[cert_file]"]');
                        if (certFileElement.length > 0) {
                            uboData.cert_file = certFileElement.val();
                        }

                        var residenceDocElement = uboContainer.find('[name*="[residence_doc]"]');
                        if (residenceDocElement.length > 0) {
                            uboData.residence_doc = residenceDocElement.val();
                        }

                        var holdCertPhotoElement = uboContainer.find('[name*="[hold_cert_photo]"]');
                        if (holdCertPhotoElement.length > 0) {
                            uboData.hold_cert_photo = holdCertPhotoElement.val();
                        }

                        // 查找Director的hasMany容器
                        var directorFieldset = $('fieldset').filter(function() {
                            return $(this).find('legend').text().indexOf('董事') !== -1;
                        });

                        if (directorFieldset.length > 0) {
                            // 查找添加按钮
                            var addButton = directorFieldset.find('.btn-success').filter(function() {
                                return $(this).text().indexOf('新增') !== -1 || $(this).attr('title') === '新增';
                            });

                            if (addButton.length > 0) {
                                addButton.click();

                                // 等待DOM更新后填充数据
                                setTimeout(function() {
                                    // 查找最新添加的Director表单
                                    var directorForms = directorFieldset.find('.has-many-forms > .has-many-form');
                                    var newDirectorForm = directorForms.last();

                                    if (newDirectorForm.length > 0) {
                                        // 填充数据到新的Director表单
                                        fields.forEach(function(field) {
                                            var targetElement = newDirectorForm.find('[name*="[' + field + ']"]');
                                            if (targetElement.length > 0 && uboData[field]) {
                                                if (targetElement.is('select')) {
                                                    targetElement.val(uboData[field]).trigger('change');
                                                } else if (targetElement.is('input[type="radio"]')) {
                                                    targetElement.filter('[value="' + uboData[field] + '"]').prop('checked', true).trigger('change');
                                                } else {
                                                    targetElement.val(uboData[field]).trigger('change');
                                                }
                                            }
                                        });

                                        // 设置is_listed_in_nar1为true（因为是从UBO复制过来的）
                                        var nar1Element = newDirectorForm.find('[name*="[is_listed_in_nar1]"]');
                                        if (nar1Element.length > 0) {
                                            nar1Element.filter('[value="1"]').prop('checked', true).trigger('change');
                                        }

                                        // 显示成功提示
                                        Dcat.swal.success('UBO数据已自动复制到董事信息');
                                    }
                                }, 800);
                            }
                        }
                    }

                    // 监听UBO的is_listed_in_nar1字段变化
                    $(document).off('change.ubo-nar1').on('change.ubo-nar1', '[data-ubo-nar1="trigger"]', function () {
                        console.log($(this).val());
                        if ($(this).val() == '1') {
                            var uboContainer = $(this).closest('.has-many-form');
                    
                            Dcat.swal.confirm(
                                '检测到您将此UBO设置为在NAR1表格中列出，是否自动将此UBO信息复制到董事信息中？',
                                '',
                                function () {
                                    copyUboToDirector(uboContainer);
                                }
                            );
                        }
                    });

                JS
            );

            $country  = DirectoryCountry::pluck('name', 'isoa2');
            $currency = DirectoryCurrency::pluck('code', 'code');
            // 企业基本信息
            $form->tab(admin_trans_label('company_details'), function (Form $form) use ($country, $currency, $kyc) {
                if ($kyc && $kyc->merchant_remark) {
                    $form->row(function (Row $row) use ($kyc) {
                        $row->html("<div class='alert alert-warning alert-dismissible fade show' role='alert' style='white-space: pre-wrap;'>$kyc->merchant_remark<button type='button' class='close' data-dismiss='alert' aria-label='Close'>
                                                <span aria-hidden='true'>&times;</span>
                                              </button>
                                          </div>")->disable();
                    });
                }

                $form->hidden('id');
                $form->hidden('merchant_id')->default(Merchant::createMerchantId());
                $form->hidden('risk_case_id');
                $filepath = request()->input('_token');
                $form->row(function (Row $row) use ($country,$filepath) {
                    $row->width(4)->select('type', admin_trans_field('country'))->options(admin_trans('kyc.options.type'))->default(MerchantKycModel::TYPE_CHINESE_HONGKONG)->required();
                    $row->width(4)->select('business_type')->options(admin_trans('kyc.options.business_type'))->default(MerchantKycModel::BUSINESS_TYPE_ORDER)->required();
                    $row->width(4)->file('comp_reg_cert')->move($filepath)->downloadable()->accept('jpg,png,gif,jpeg,pdf,xlsx')->autoUpload()->help(admin_trans_field('help.comp_reg_cert'))->required();

                    $row->width(4)->text('company_name')->required();
                    $row->width(4)->text('reg_cert_no')->required();
                    $row->width(4)->text('company_address')->required();

                    $row->width(4)->date('found_date')->required();
                    $row->width(4)->file('business_reg_cert')->move($filepath)->downloadable()->accept('jpg,png,gif,jpeg,pdf,xlsx')->help(admin_trans_field('help.business_reg_cert'))->autoUpload()->required();
                    $row->width(4)->text('business_reg_no')->required();

                    $row->width(4)->date('cert_validity')->required();
                    $row->width(4)->file('fin_statement')->move($filepath)->downloadable()->accept('jpg,png,gif,jpeg,pdf,xlsx')->autoUpload()->help(admin_trans_field('help.fin_statement'));
                    $row->width(4)->file('shareholder_structure')->move($filepath)->downloadable()->accept('jpg,png,gif,jpeg,pdf,xlsx')->autoUpload()->help(admin_trans_field('help.shareholder_structure'))->required();

                    $row->width(4)->file('annual_rpt')->move($filepath)->downloadable()->autoUpload()->accept('jpg,png,gif,jpeg,pdf,xlsx')->help(admin_trans_field('help.annual_rpt'));
                    $row->width(4)->file('bank_statement')->move($filepath)->downloadable()->accept('jpg,png,gif,jpeg,pdf,xlsx')->autoUpload()->help(admin_trans_field('help.bank_statement'))->required();
                    $row->width(4)->file('comp_addr_cert')->move($filepath)->downloadable()->accept('jpg,png,gif,jpeg,pdf,xlsx')->autoUpload()->help(admin_trans_field('help.comp_addr_cert'))->required();

                    $row->width(4)->mobile('office_phone');
                    $row->width(4)->multipleSelect('export_country')->options($country)->required();
                });

                $form->fieldset(admin_trans_label('business_info'), function (Form $form) use ($country, $currency) {
                    $form->row(function (Row $row) use ($country, $currency) {
                        $row->width(3)->text('nature_of_business')->required();
                        $row->width(3)->select('business_model')->options(admin_trans('kyc.options.business_model'))->when(MerchantKycModel::BUSINESS_MODEL_ONLINE, function (Form $form) {
                            $form->width(3)->text('merchant_portal_url_app')->rules('required_if:business_model,=,' . MerchantKycModel::BUSINESS_MODEL_ONLINE, ['required']);
                        })->when(MerchantKycModel::BUSINESS_MODEL_OFFLINE, function (Form $form) {
                            $form->array('store_info', function (Form\NestedForm $table) {
                                $table->width(3)->text('store_name_zh');
                                $table->width(3)->text('store_name_en');
                                $table->width(3)->select('store_address')->options(admin_trans('kyc.options.store_address'))->when(
                                    MerchantKycModel::STORE_ADDRESS_TYPE_BUSINESS,
                                    function (Form\NestedForm $form) {
                                        $form->textarea('store_business_address')->rules('required_if:store_address,=,' . MerchantKycModel::STORE_ADDRESS_TYPE_BUSINESS, ['required']);
                                    }
                                )->when(MerchantKycModel::STORE_ADDRESS_TYPE_ES, function (Form\NestedForm $form) {
                                    $form->textarea('store_es_address')->rules('required_if:store_address,=,' . MerchantKycModel::STORE_ADDRESS_TYPE_ES, ['required']);
                                })->required();
                            })->rules('required_if:business_model,=,' . MerchantKycModel::BUSINESS_MODEL_ONLINE, ['required'])->saveAsJson();
                        })->required();

                        $row->width(3)->text('mcc_code')->rules('nullable|digits:4');
                        $row->width(3)->select('payment_channel')->options(admin_trans('kyc.options.payment_channel'))->required();
                        //                        $row->width(3)->rate('transaction_fee_rate');
                        //                        $row->width(3)->rate('additional_fees');
                        //                        $row->width(3)->number('settlement_cycle');
                        $row->width(3)->multipleSelect('transaction_currency')->options($currency);
                        $row->width(3)->select('settlement_currency')->options($currency)->required();
                    });

                    $form->row(function (Row $row) {
                        $row->width(3)->currency('average_transaction_value')->default(0)->symbol('USD');
                        $row->width(3)->number('avg_daily_transaction_count')->default(0);
                        $row->width(3)->currency('avg_daily_transaction_volume')->default(0)->symbol('USD');
                        $row->width(3)->number('avg_monthly_transaction_count')->default(0);
                        $row->width(3)->currency('avg_monthly_transaction_volume')->default(0)->symbol('USD');
                    });
                });

                $form->fieldset(admin_trans_label('holding_co'), function (Form $form) use ($country) {
                    // 控股公司
                    $form->table('holding_co', function (Form\NestedForm $table) use ($country) {
                        $table->text('company_name')->required();
                        $table->text('reg_cert_no')->required();
                        $table->rate('equity_ratio')->required()->maxLength(3);
                        $table->select('country')->options($country)->required();
                        $table->textarea('company_address')->required();
                    })->width(12)->required();
                });

                $form->fieldset(admin_trans_label('ubo'), function (Form $form) use ($country, $filepath) {
                    $form->hasMany('ubo', function (Form\NestedForm $form) use ($country, $filepath) {
                        $form->column(6, function (Form\NestedForm $form) use ($country, $filepath) {
                            $form->hidden('id');
                            $form->hidden('beneficial_type')->value(MerchantKycBeneficial::BENEFICIAL_TYPE_UBO);
                            $form->select('cert_type')->options(admin_trans('kyc.options.cert_type'))->required();
                            $form->select('nationality')->options($country)->required();
                            $form->text('name')->required();
                            $form->rate('share_ratio')->required()->maxLength(3);
                            $form->date('birth_date')->required();
                            $form->text('address')->required();
                            $form->multipleFile('cert_file')->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload()->limit(2)->required();
                        });

                        $form->column(6, function (Form\NestedForm $form) use ($filepath) {
                            $form->radio('is_listed_in_nar1')->options(admin_trans('kyc.options.confirm'))->required()->attribute(['data-ubo-nar1' => 'trigger']);
                            $form->text('cert_number')->required();
                            $form->file('residence_doc')->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload()->help(admin_trans_field('help.residence_doc'))->required();
                            $form->radio('is_signatory')->options(admin_trans('kyc.options.confirm'))->when(true, function (Form\NestedForm $form) use ($filepath) {
                                $form->file('hold_cert_photo')->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload();
                            })->required();
                        });
                    });
                });

                $form->fieldset(admin_trans_label('director'), function (Form $form) use ($country, $filepath) {
                    $form->hasMany('director', function (Form\NestedForm $form) use ($country, $filepath) {
                        $form->column(6, function (Form\NestedForm $form) use ($country, $filepath) {
                            $form->hidden('id');
                            $form->hidden('beneficial_type')->value(MerchantKycBeneficial::BENEFICIAL_TYPE_DIRECTOR);
                            $form->select('cert_type')->options(admin_trans('kyc.options.cert_type'))->required();
                            $form->select('nationality')->options($country)->required();
                            $form->text('name')->required();
                            $form->rate('share_ratio')->required()->maxLength(3);
                            $form->date('birth_date')->required();
                            $form->text('address')->required();
                            $form->multipleFile('cert_file')->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload()->required();
                        });

                        $form->column(6, function (Form\NestedForm $form) use ($filepath) {
                            $form->radio('is_listed_in_nar1', admin_trans_field('is_listed_in_nar1_b'))->options(admin_trans('kyc.options.confirm'))->required();
                            $form->text('cert_number')->required();
                            $form->file('residence_doc')->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload()->help(admin_trans_field('help.residence_doc'))->required();
                            $form->radio('is_signatory', admin_trans_field('is_signatory_b'))->options(admin_trans('kyc.options.confirm'))->when(true, function (Form\NestedForm $form) use ($filepath) {
                                $form->file('hold_cert_photo', admin_trans_field('hold_cert_photo_b'))->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload();
                            })->required();
                        });
                    })->attribute(['class' => 'director-container']);
                });

                $form->fieldset(admin_trans_label('authorized'), function (Form $form) use ($country, $filepath) {
                    $form->hasMany('authorized', function (Form\NestedForm $form) use ($country, $filepath) {
                        $form->column(6, function (Form\NestedForm $form) use ($country, $filepath) {
                            $form->hidden('id');
                            $form->hidden('beneficial_type')->value(MerchantKycBeneficial::BENEFICIAL_TYPE_AUTHORITY);
                            $form->select('is_signatory', admin_trans_field('is_signatory_c'))
                                 ->options(admin_trans('kyc.options.is_signatory'))
                                 ->default(1)
                                 ->rules('required')
                                 ->attribute(['data-select' => 'select-is-signatory']);
                            $form->select('cert_type')->options(admin_trans('kyc.options.cert_type'))->default(1)->attribute(['data-signatory-item' => 'display']);
                            $form->select('nationality')->options($country)->attribute(['data-signatory-item' => 'display']);
                            $form->text('name')->attribute(['data-signatory-item' => 'display']);
                            $form->text('address')->attribute(['data-signatory-item' => 'display']);
                            $form->multipleFile('cert_file')->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload()->attribute(['data-signatory-item' => 'display']);
                            $form->date('birth_date')->attribute(['data-signatory-item' => 'display']);
                        });
                        $form->column(6, function (Form\NestedForm $form) use ($country, $filepath) {
                            $form->text('cert_number')->attribute(['data-signatory-item' => 'display']);
                            $form->file('residence_doc')->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload()->help(admin_trans_field('help.residence_doc'))->attribute(['data-signatory-item' => 'display']);
                            $form->file('hold_cert_photo', admin_trans_field('hold_cert_photo_c'))->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload()->attribute(['data-signatory-item' => 'display']);
                        });
                        Admin::script(
                            <<<JS
                                $(document).ready(function(){
                                var formItems = $('[data-signatory-item="display"]')
                                 formItems.closest('.form-field').hide();
                                    $("[data-select='select-is-signatory']").on("change",function(){
                                        const val= $(this).val()
                                        if(val==0){
                                            formItems.closest('.form-field').show();
                                        }else{
                                          formItems.closest('.form-field').hide();
                                        }
                                    })
                                });
JS
                        );
                    })->default(1)->disableDelete()->disableCreate();
                });

                // 业务联系人
                $form->fieldset(admin_trans_label('contacts'), function (Form $form) use ($country) {
                    $form->row(function (Row $form) {
                        $form->width(3)->text('contacts_name')->required();
                        $form->width(3)->text('contacts_phone')->required();
                        $form->width(3)->email('contacts_email')->required();
                        $form->width(3)->text('contacts_position')->required();
                    });
                });

                $form->file('merchant_apply_file')->move($filepath)->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload();
                $form->button('<span >' . admin_trans_field('download_template') . ' </span>')->on('click', "
                    window.open('/download/商戶入網申請表.xlsx');
                ");

                if (!$kyc) {
                    $form->password('password')->rules('required|min:8');
                    $form->password('password_confirmation')->rules('required|min:8');
                    $form->display('random_password')->help(admin_trans_field('help.generate_password'));
                    $form->button('<span >' . admin_trans_field('get_password') . '</span>')->on('click', "$.ajax({
                    url: '/get_random_password',
                    type: 'GET',
                    success: function(res) {
                        $('.help-block').parent().find('.box-body').text(res);
                        $('input[name=password]').val(res);
                        $('input[name=password_confirmation]').val(res);
                        navigator.clipboard.writeText(res).then(function() {
                            Dcat.swal.success('" . admin_trans_field('help.random_password') . "');
                        });
                    }
                });")->help(admin_trans_field('help.password'));
                }
            });

            // 填充数据
            if ($kyc) {
                $form->fillFields($kyc->toarray());

                if ($kyc->audit_result == MerchantKycModel::STATUS_CHECK) {
                    $form->disableFooter();

                    // 将所有的 form 表单字段改为只读
                    $form->fields()->each(function (Form\Field $field) {
                        $field->disable();
                    });
                }
            }

            $form->saving(function (Form $form) use ($kyc) {
                $uboData      = $form->input('ubo') ?? [];
                $directorData = $form->input('director') ?? [];

                if (count($uboData) < 1) {
                    return $form->response()->error(admin_trans_field('help.ubo'))->alert();
                }
                if (count($directorData) < 1) {
                    return $form->response()->error(admin_trans_field('help.director'))->alert();
                }

                // 如果 ubo 和 director 里 是否作为授权签署人都为 否 那么授权签署人需要填写
                if (!in_array(1, array_column($uboData, 'is_signatory')) && !in_array(1, array_column($directorData, 'is_signatory'))) {
                    if (empty($form->input('authorized')) || $form->input('authorized')[0]['is_signatory'] == 1) {
                        return $form->response()->error(admin_trans_field('help.authorized'))->alert();
                    }
                }

                // 需要把uboData directorData authorized 里的 id 去掉
                $form->ubo = array_map(function ($item) {
                    unset($item['id']);
                    return $item;
                }, $uboData);

                $form->director = array_map(function ($item) {
                    unset($item['id']);
                    return $item;
                }, $directorData);

                $form->authorized = array_map(function ($item) {
                    unset($item['id']);
                    return $item;
                }, $form->input('authorized'));

                $foundDate = $form->input('found_date');
                $annualRpt = $form->input('annual_rpt');

                if ($foundDate) {
                    try {
                        $diffYears = now()->diffInYears(Carbon::parse($foundDate));

                        // 不满一年且未上传年度报告
                        if ($diffYears < 1 && empty($annualRpt)) {
                            return $form->response()
                                        ->error(admin_trans_field('help.annual_rpt_required'))->alert();
                        }
                    } catch (\Exception $e) {
                        logger()->error('日期解析失败：' . $e->getMessage());
                    }
                }

                $merchant_id = $form->merchant_id ?? Merchant::createMerchantId();

                if (!$form->id) {
                    $kycService = new KycService;
                    $isRegister = $kycService->isRegister(['company_name' => $form->input('company_name'), 'contacts_email' => $form->input('contacts_email')]);
                    if (!$isRegister['status']) {
                        return $form->response()->error($isRegister['message'])->alert();
                    }

                    $form->merchant_id = $merchant_id;
                }

                $caseService = new RiskCasesService();
                $case        = $caseService->addCases([
                                                          'merchant_id'   => $merchant_id,
                                                          'merchant_name' => $form->input('company_name'),
                                                          'case_type'     => RiskCase::CASE_TYPE_KYC,
                                                          'country'       => $form->input('type') ?? -1
                                                      ]);

                $form->risk_case_id = $case->id;
                // 排除掉忽略的字段
                $form->deleteInput(['password', 'password_confirmation', 'id', 'edd_user_id', 'audit_remark', 'files', 'merchant_remark']);
            });

            $form->saved(function (Form $form, $result) {
                if (!$result) {
                    return $form->response()->error(admin_trans_field('tip.system_error'))->alert();
                }

                LexisNexisService::merchantKycLexisNexisSearch(
                    [
                        'company_name'    => $form->input('company_name'),
                        'company_address' => $form->input('company_address'),
                        'reg_cert_no'     => $form->input('reg_cert_no'),
                        'business_reg_no' => $form->input('business_reg_no'),
                    ],
                    $form->input('holding_co') ?? [],
                    $form->input('ubo') ?? [],
                    $form->input('director') ?? [],
                    $form->merchant_id
                );

                // 是否新商户注册
                if (!auth()->user()) {
                    try {
                        DB::beginTransaction();

                        $merchant                = new Merchant();
                        $merchant->merchant_id   = $form->merchant_id;
                        $merchant->api_token     = Merchant::getApiToken();
                        $merchant->merchant_name = $form->input('company_name');
                        $merchant->phone         = $form->input('contacts_phone');
                        $merchant->email         = $form->input('contacts_email');
                        $merchant->status        = Merchant::STATUS_CHECK;
                        $merchant->is_credit     = $form->input('business_type') == MerchantKycModel::BUSINESS_TYPE_ORDER ? Merchant::STATUS_ENABLE : Merchant::STATUS_DISABLE;
                        $merchant->is_virtual    = $form->input('business_type') == MerchantKycModel::BUSINESS_TYPE_VIRTUAL ? Merchant::STATUS_ENABLE : Merchant::STATUS_DISABLE;

                        $merchantStatus = $merchant->save();

                        if (!$merchantStatus) {
                            throw new Exception(admin_trans_field('tip.system_error'));
                        }

                        // 创建商户主用户
                        $merchant->users()->createMany([[
                                                            'type'              => User::TYPE_MAIN,
                                                            'merchant_id'       => $form->merchant_id,
                                                            'name'              => 'admin',
                                                            'password'          => bcrypt(request()->get('password')),
                                                            'status'            => User::STATUS_ENABLE,
                                                            'email'             => $form->input('contacts_email'),
                                                            'email_verified_at' => now(),
                                                            'password_valid_at' => now()->addMonths(3),
                                                            '_remove_'          => '0'
                                                        ]]);

                        if (config('open_im.open_im')) {
                            // 根据mid 查询商户的admin用户
                            $user = $merchant->users[0];
                            // 注册openIm
                            $openImUser = [
                                'userId'          => $user['id'],
                                'userType'        => OpenImUser::USER_TYPE_MERCHANT_MAIN,
                                'nickname'        => $merchant->merchant_name,
                                'password'        => $user['password'],
                                'addGroup'        => true,
                                'addFriend'       => false,
                                'createGroup'     => false,
                                'defaultAddGroup' => false,
                                'groupRoleLevel'  => OpenImUser::ROLE_LEVEL_MEMBER,
                                'userName'        => 'system',
                            ];
                            $openIm     = new OpenImService;
                            $userId     = $openIm->register($openImUser, $merchant->merchant_id, $merchant->merchant_name);
                            if ($userId) {
                                // 自动创建群组
                                $group = $openIm->createGroup($merchant->merchant_name, [$userId]);
                            }

                            if (!$userId || !$group) {
                                logger()->channel('intercept')->warning('open—im 注册异常 userid:' . $userId);
                                dispatch(new SendSlsLog(
                                             ['message' => 'open—im 注册异常 userid:' . $userId],
                                             [],
                                             'warning',
                                             'intercept'
                                         ));
                            }
                        }

                        DB::commit();
                    } catch (\Throwable $th) {
                        logger()->channel('intercept')->warning('商户申请Kyc异常:' . $th->getMessage());
                        dispatch(new SendSlsLog(
                                     ['message' => '商户申请Kyc异常:' . $th->getMessage()],
                                     [],
                                     'warning',
                                     'intercept'
                                 ));

                        DB::rollBack();
                        return $form->response()->error(admin_trans_field('tip.system_error'))->alert();
                    }

                    //发送邮件
                    $templateData = [
                        'address' => $merchant->email,
                        'user'    => $merchant->merchant_name,
                        'view'    => 'KycRegister',
                        'data'    => [
                            'company_name' => $merchant->merchant_name,
                            'merchant_id'  => $merchant->merchant_id,
                            'username'     => 'admin',
                        ]
                    ];
                    // 添加邮件任务到队列
                    dispatch(new SendEmail($templateData, 0));
                }

                return $form->response()->success(admin_trans_field('tip.success'))->location(admin_url('auth/login'));
            });
        });
    }
}
